{
	"name": "chalk",
	"version": "4.1.2",
	"description": "Terminal string styling done right",
	"license": "MIT",
	"repository": "chalk/chalk",
	"funding": "https://github.com/chalk/chalk?sponsor=1",
	"main": "source",
	"engines": {
		"node": ">=10"
	},
	"scripts": {
		"test": "xo && nyc ava && tsd",
		"bench": "matcha benchmark.js"
	},
	"files": [
		"source",
		"index.d.ts"
	],
	"keywords": [
		"color",
		"colour",
		"colors",
		"terminal",
		"console",
		"cli",
		"string",
		"str",
		"ansi",
		"style",
		"styles",
		"tty",
		"formatting",
		"rgb",
		"256",
		"shell",
		"xterm",
		"log",
		"logging",
		"command-line",
		"text"
	],
	"dependencies": {
		"ansi-styles": "^4.1.0",
		"supports-color": "^7.1.0"
	},
	"devDependencies": {
		"ava": "^2.4.0",
		"coveralls": "^3.0.7",
		"execa": "^4.0.0",
		"import-fresh": "^3.1.0",
		"matcha": "^0.7.0",
		"nyc": "^15.0.0",
		"resolve-from": "^5.0.0",
		"tsd": "^0.7.4",
		"xo": "^0.28.2"
	},
	"xo": {
		"rules": {
			"unicorn/prefer-string-slice": "off",
			"unicorn/pref